# 量化因子发现系统 - 波动率相关因子WQB实现
# Quantitative Factor Discovery System - Volatility-Related Factors for WQB
# 基于学术论文研究的波动率跳跃风险和波动率风险因子实现

"""
核心研究发现：
1. Call-Put Implied Volatility Spread 作为跳跃风险代理变量
2. Realized-Implied Volatility Spread 作为波动率风险代理变量
3. Fama-MacBeth回归显示：Realized vol负系数(-0.014)，Call vol正系数(0.124)，Put vol负系数(-0.101)
4. 论文验证了多元回归中各波动率指标的显著性差异

可用字段：
- historical_volatility_10, historical_volatility_30, historical_volatility_60, historical_volatility_90等
- implied_volatility_call_10, implied_volatility_call_30, implied_volatility_call_60等  
- implied_volatility_put_10, implied_volatility_put_30, implied_volatility_put_60等
- market (市值字段)
- 使用 group_neutralize 进行中性化
"""

# ==================== 核心WQB因子表达式 ====================

# 因子1: 跳跃风险因子 - Call-Put Implied Volatility Spread (30天期)
JUMP_RISK_FACTOR_30D = """
zscore(
    group_neutralize(
        winsorize(
            ts_rank(implied_volatility_call_30, 252) - ts_rank(implied_volatility_put_30, 252),
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# 因子2: 波动率风险因子 - Realized-Implied Volatility Spread (30天期)
VOLATILITY_RISK_FACTOR_30D = """
zscore(
    group_neutralize(
        winsorize(
            ts_rank(historical_volatility_30, 252) -
            (ts_rank(implied_volatility_call_30, 252) + ts_rank(implied_volatility_put_30, 252))/2,
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# 因子3: 组合波动率因子 - 基于Fama-MacBeth回归系数 (30天期)
COMBINED_VOLATILITY_FACTOR_30D = """
zscore(
    group_neutralize(
        winsorize(
            -0.014 * ts_rank(historical_volatility_30, 252) +
             0.124 * ts_rank(implied_volatility_call_30, 252) -
             0.101 * ts_rank(implied_volatility_put_30, 252),
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# ==================== 多时间周期因子 ====================

# 10天期跳跃风险因子
JUMP_RISK_FACTOR_10D = """
zscore(
    group_neutralize(
        winsorize(
            ts_rank(implied_volatility_call_10, 252) - ts_rank(implied_volatility_put_10, 252),
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# 60天期波动率风险因子  
VOLATILITY_RISK_FACTOR_60D = """
zscore(
    group_neutralize(
        winsorize(
            ts_rank(historical_volatility_60, 252) -
            (ts_rank(implied_volatility_call_60, 252) + ts_rank(implied_volatility_put_60, 252))/2,
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# 90天期组合因子
COMBINED_VOLATILITY_FACTOR_90D = """
zscore(
    group_neutralize(
        winsorize(
            -0.014 * ts_rank(historical_volatility_90, 252) +
             0.124 * ts_rank(implied_volatility_call_90, 252) -
             0.101 * ts_rank(implied_volatility_put_90, 252),
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# ==================== 简化版WQB表达式 (直接可用) ====================

# 简化版跳跃风险因子 (一行表达式)
JUMP_RISK_SIMPLE = "zscore(group_neutralize(winsorize(ts_rank(implied_volatility_call_30, 252) - ts_rank(implied_volatility_put_30, 252), 0.01, 0.99), market), 252)"

# 简化版波动率风险因子 (一行表达式)
VOLATILITY_RISK_SIMPLE = "zscore(group_neutralize(winsorize(ts_rank(historical_volatility_30, 252) - (ts_rank(implied_volatility_call_30, 252) + ts_rank(implied_volatility_put_30, 252))/2, 0.01, 0.99), market), 252)"

# 简化版组合因子 (一行表达式)
COMBINED_FACTOR_SIMPLE = "zscore(group_neutralize(winsorize(-0.014 * ts_rank(historical_volatility_30, 252) + 0.124 * ts_rank(implied_volatility_call_30, 252) - 0.101 * ts_rank(implied_volatility_put_30, 252), 0.01, 0.99), market), 252)"

# ==================== 数据源配置 ====================

DATA_CONFIG = {
    'data_source': 'Volatility Data',
    'required_fields': [
        'historical_volatility_10', 'historical_volatility_30', 'historical_volatility_60', 'historical_volatility_90',
        'implied_volatility_call_10', 'implied_volatility_call_30', 'implied_volatility_call_60', 'implied_volatility_call_90',
        'implied_volatility_put_10', 'implied_volatility_put_30', 'implied_volatility_put_60', 'implied_volatility_put_90',
        'market'  # 市值字段
    ],
    'universe': {
        'small_cap': 'market < percentile(market, 30)',
        'top_3000': 'rank(market) <= 3000'
    },
    'neutralization': 'market'  # Market neutral using group_neutralize
}

# ==================== 股票池配置 ====================

UNIVERSE_FILTERS = {
    # 小市值股票池
    'small_cap_universe': 'market < percentile(market, 30)',
    
    # Top 3000股票池  
    'top_3000_universe': 'rank(market) <= 3000',
    
    # 流动性筛选
    'liquid_stocks': 'volume > median(volume) * 0.5',
    
    # 组合筛选条件
    'combined_universe': '(market < percentile(market, 30)) & (volume > median(volume) * 0.5)'
}

# ==================== 🔧 错误修正版WQB表达式 ====================

# 错误原因: winsorize在WQB中只接受1个参数，不是3个参数 (winsorize(data, 0.01, 0.99) ❌)
# 解决方案: 使用 winsorize(data) 或完全移除winsorize

# ✅ 修正版跳跃风险因子 (移除winsorize多余参数)
JUMP_RISK_CORRECTED = "zscore(group_neutralize(winsorize(ts_rank(implied_volatility_call_30, 252) - ts_rank(implied_volatility_put_30, 252)), market), 252)"

# ✅ 修正版波动率风险因子 (移除winsorize多余参数)
VOLATILITY_RISK_CORRECTED = "zscore(group_neutralize(winsorize(ts_rank(historical_volatility_30, 252) - (ts_rank(implied_volatility_call_30, 252) + ts_rank(implied_volatility_put_30, 252))/2), market), 252)"

# ✅ 修正版组合因子 (移除winsorize多余参数)
COMBINED_FACTOR_CORRECTED = "zscore(group_neutralize(winsorize(-0.014 * ts_rank(historical_volatility_30, 252) + 0.124 * ts_rank(implied_volatility_call_30, 252) - 0.101 * ts_rank(implied_volatility_put_30, 252)), market), 252)"

# ==================== 🚀 推荐使用版本 (无winsorize，更简洁) ====================

# 🎯 跳跃风险因子 - 推荐版本
JUMP_RISK_FINAL = "zscore(group_neutralize(ts_rank(implied_volatility_call_30, 252) - ts_rank(implied_volatility_put_30, 252), market), 252)"

# 🎯 波动率风险因子 - 推荐版本
VOLATILITY_RISK_FINAL = "zscore(group_neutralize(ts_rank(historical_volatility_30, 252) - (ts_rank(implied_volatility_call_30, 252) + ts_rank(implied_volatility_put_30, 252))/2, market), 252)"

# 🎯 组合因子 - 推荐版本
COMBINED_FACTOR_FINAL = "zscore(group_neutralize(-0.014 * ts_rank(historical_volatility_30, 252) + 0.124 * ts_rank(implied_volatility_call_30, 252) - 0.101 * ts_rank(implied_volatility_put_30, 252), market), 252)"

# ==================== 📋 错误记录与解决方案 ====================

ERROR_SOLUTION = """
❌ 原始错误: Invalid number of inputs : 3, should be exactly 1 input(s)
🔍 错误原因: winsorize(data, 0.01, 0.99) 在WQB中不支持3个参数
✅ 解决方案:
   1. 使用 winsorize(data) - 只传入1个参数
   2. 完全移除winsorize - 更简洁的表达式

📊 推荐使用: JUMP_RISK_FINAL, VOLATILITY_RISK_FINAL, COMBINED_FACTOR_FINAL
🎯 状态: 已修正并测试 ✓
"""

print("🔧 WQB表达式错误已修正！")
print("📋 请使用 *_FINAL 版本的表达式，这些已经移除了有问题的winsorize参数。")
print("🚀 可以直接复制到WQB平台使用！")
