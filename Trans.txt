# 量化因子发现系统 - 波动率相关因子WQB实现
# Quantitative Factor Discovery System - Volatility-Related Factors for WQB
# 基于学术论文研究的波动率跳跃风险和波动率风险因子实现

"""
核心研究发现：
1. Call-Put Implied Volatility Spread 作为跳跃风险代理变量
2. Realized-Implied Volatility Spread 作为波动率风险代理变量
3. Fama-MacBeth回归显示：Realized vol负系数(-0.014)，Call vol正系数(0.124)，Put vol负系数(-0.101)
4. 论文验证了多元回归中各波动率指标的显著性差异

可用字段：
- historical_volatility_10, historical_volatility_30, historical_volatility_60, historical_volatility_90等
- implied_volatility_call_10, implied_volatility_call_30, implied_volatility_call_60等  
- implied_volatility_put_10, implied_volatility_put_30, implied_volatility_put_60等
- market (市值字段)
- 使用 group_neutralize 进行中性化
"""

# ==================== 核心WQB因子表达式 ====================

# 因子1: 跳跃风险因子 - Call-Put Implied Volatility Spread (30天期)
JUMP_RISK_FACTOR_30D = """
zscore(
    group_neutralize(
        winsorize(
            ts_rank(implied_volatility_call_30, 252) - ts_rank(implied_volatility_put_30, 252),
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# 因子2: 波动率风险因子 - Realized-Implied Volatility Spread (30天期)
VOLATILITY_RISK_FACTOR_30D = """
zscore(
    group_neutralize(
        winsorize(
            ts_rank(historical_volatility_30, 252) -
            (ts_rank(implied_volatility_call_30, 252) + ts_rank(implied_volatility_put_30, 252))/2,
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# 因子3: 组合波动率因子 - 基于Fama-MacBeth回归系数 (30天期)
COMBINED_VOLATILITY_FACTOR_30D = """
zscore(
    group_neutralize(
        winsorize(
            -0.014 * ts_rank(historical_volatility_30, 252) +
             0.124 * ts_rank(implied_volatility_call_30, 252) -
             0.101 * ts_rank(implied_volatility_put_30, 252),
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# ==================== 多时间周期因子 ====================

# 10天期跳跃风险因子
JUMP_RISK_FACTOR_10D = """
zscore(
    group_neutralize(
        winsorize(
            ts_rank(implied_volatility_call_10, 252) - ts_rank(implied_volatility_put_10, 252),
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# 60天期波动率风险因子  
VOLATILITY_RISK_FACTOR_60D = """
zscore(
    group_neutralize(
        winsorize(
            ts_rank(historical_volatility_60, 252) -
            (ts_rank(implied_volatility_call_60, 252) + ts_rank(implied_volatility_put_60, 252))/2,
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# 90天期组合因子
COMBINED_VOLATILITY_FACTOR_90D = """
zscore(
    group_neutralize(
        winsorize(
            -0.014 * ts_rank(historical_volatility_90, 252) +
             0.124 * ts_rank(implied_volatility_call_90, 252) -
             0.101 * ts_rank(implied_volatility_put_90, 252),
            0.01, 0.99
        ),
        market
    ),
    252
)
"""

# ==================== 简化版WQB表达式 (直接可用) ====================

# 简化版跳跃风险因子 (一行表达式)
JUMP_RISK_SIMPLE = "zscore(group_neutralize(winsorize(ts_rank(implied_volatility_call_30, 252) - ts_rank(implied_volatility_put_30, 252), 0.01, 0.99), market), 252)"

# 简化版波动率风险因子 (一行表达式)
VOLATILITY_RISK_SIMPLE = "zscore(group_neutralize(winsorize(ts_rank(historical_volatility_30, 252) - (ts_rank(implied_volatility_call_30, 252) + ts_rank(implied_volatility_put_30, 252))/2, 0.01, 0.99), market), 252)"

# 简化版组合因子 (一行表达式)
COMBINED_FACTOR_SIMPLE = "zscore(group_neutralize(winsorize(-0.014 * ts_rank(historical_volatility_30, 252) + 0.124 * ts_rank(implied_volatility_call_30, 252) - 0.101 * ts_rank(implied_volatility_put_30, 252), 0.01, 0.99), market), 252)"

# ==================== 数据源配置 ====================

DATA_CONFIG = {
    'data_source': 'Volatility Data',
    'required_fields': [
        'historical_volatility_10', 'historical_volatility_30', 'historical_volatility_60', 'historical_volatility_90',
        'implied_volatility_call_10', 'implied_volatility_call_30', 'implied_volatility_call_60', 'implied_volatility_call_90',
        'implied_volatility_put_10', 'implied_volatility_put_30', 'implied_volatility_put_60', 'implied_volatility_put_90',
        'market'  # 市值字段
    ],
    'universe': {
        'small_cap': 'market < percentile(market, 30)',
        'top_3000': 'rank(market) <= 3000'
    },
    'neutralization': 'market'  # Market neutral using group_neutralize
}

# ==================== 股票池配置 ====================

UNIVERSE_FILTERS = {
    # 小市值股票池
    'small_cap_universe': 'market < percentile(market, 30)',
    
    # Top 3000股票池  
    'top_3000_universe': 'rank(market) <= 3000',
    
    # 流动性筛选
    'liquid_stocks': 'volume > median(volume) * 0.5',
    
    # 组合筛选条件
    'combined_universe': '(market < percentile(market, 30)) & (volume > median(volume) * 0.5)'
}

# ==================== 验证逻辑实现 ====================

VALIDATION_FRAMEWORK = """
# 论文验证逻辑实现
# 1. Fama-MacBeth回归验证
def fama_macbeth_validation():
    # 期望系数符号验证：
    # Realized volatility: 负系数 (约-0.014, t-stat=-2.3)
    # Call volatility: 正系数 (约0.124, t-stat=6.4)
    # Put volatility: 负系数 (约-0.101, t-stat=-6.1)

    expected_results = {
        'realized_vol_coeff': -0.014,
        'call_vol_coeff': 0.124,
        'put_vol_coeff': -0.101,
        'significance_threshold': 2.0  # t-stat阈值
    }

    return expected_results

# 2. Long-Short策略回测
def long_short_backtest():
    # 基于因子值构建Long-Short组合
    strategy_logic = '''
    # 按因子值分组
    factor_quintiles = qcut(combined_volatility_factor, 5)

    # 构建Long-Short组合
    long_portfolio = factor_quintiles == 5   # 最高分位
    short_portfolio = factor_quintiles == 1  # 最低分位

    # 计算策略收益
    strategy_return = long_portfolio.mean() - short_portfolio.mean()
    '''

    return strategy_logic

# 3. 因子有效性检验
def factor_effectiveness_test():
    # IC (Information Coefficient) 分析
    # 预期IC > 0.05 且显著

    effectiveness_metrics = {
        'target_ic': 0.05,
        'target_ic_ir': 1.5,  # IC信息比率
        'target_correlation': 0.6,   # 基于论文图表显示的最高相关性
        'min_coverage': 0.8   # 最小覆盖率
    }

    return effectiveness_metrics
"""

# ==================== 使用说明 ====================

USAGE_INSTRUCTIONS = """
WQB平台使用说明：

1. 直接复制以下表达式到WQB平台：
   - 跳跃风险因子: JUMP_RISK_SIMPLE
   - 波动率风险因子: VOLATILITY_RISK_SIMPLE
   - 组合因子: COMBINED_FACTOR_SIMPLE

2. 数据要求：
   - 确保有历史波动率和隐含波动率数据
   - 建议使用30天期数据作为主要因子
   - 可测试10天、60天、90天期的效果差异

3. 验证步骤：
   - 检查因子IC值是否 > 0.05
   - 验证回归系数符号是否符合论文预期
   - 测试Long-Short策略收益率

4. 优化建议：
   - 可调整winsorize参数 (当前0.01, 0.99)
   - 可调整ts_rank窗口期 (当前252天)
   - 可测试不同的中性化方法
"""

print("量化因子发现系统实现完成！")
print("核心WQB表达式已生成，可直接在WorldQuant Brain平台使用。")
print("请参考USAGE_INSTRUCTIONS部分的使用说明。")
